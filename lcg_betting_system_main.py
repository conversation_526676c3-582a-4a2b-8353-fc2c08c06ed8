#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LCG随机投注系统主程序 - 增强版
完整的实时记录功能和美观的Markdown报告生成
集成真实游戏投注功能
"""

import time
import random
from datetime import datetime
from optimized_random_betting_system import OptimizedRandomBettingSystem
from api_framework import GameAPIClient, GameMonitor, GameState

# 导入真实投注相关组件
try:
    from prediction_strategy_adapter import PredictionRuleAdapter
    from profitable_betting_strategy import ProfitableBettingStrategy
    from smart_betting_handler import SmartBettingHandler
    from real_time_logger import log_prediction, log_room_selection, log_betting, log_result
    REAL_BETTING_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 真实投注组件未找到: {e}")
    print("   将使用模拟投注模式")
    REAL_BETTING_AVAILABLE = False


class LCGBettingSystemMain:
    """LCG投注系统主程序 - 增强版"""

    def __init__(self):
        self.system = None
        self.is_running = False
        self.real_betting_mode = False
        self.api_client = None

        # 真实投注组件
        self.prediction_adapter = None
        self.profitable_strategy = None
        self.smart_betting_handler = None

        print("🎲 LCG随机投注系统 v2.1 - 增强版")
        print("=" * 60)
        print("✨ 特性:")
        print("   🧮 线性同余生成器 (LCG) 最优随机算法")
        print("   💰 增强版动态金额管理")
        print("   📊 完整实时记录功能")
        print("   📝 美观Markdown报告生成")
        print("   ⚠️ 多层风险控制机制")
        if REAL_BETTING_AVAILABLE:
            print("   🎯 真实游戏投注功能")
            print("   🤖 智能投注处理器")
            print("   📈 盈利策略引擎")
        print("=" * 60)
    
    def initialize_system(self, config: dict = None, real_betting: bool = False):
        """初始化投注系统"""

        if config is None:
            config = self.get_default_config()

        print(f"\n🚀 正在初始化LCG投注系统...")

        # 根据模式创建API客户端
        if real_betting and REAL_BETTING_AVAILABLE:
            print("🎯 启用真实投注模式")
            self.real_betting_mode = True
            self.api_client = self.create_real_api_client()

            # 初始化真实投注组件
            self.init_real_betting_components()
        else:
            print("🎮 使用模拟投注模式")
            self.real_betting_mode = False
            self.api_client = self.create_mock_api_client()

        # 创建优化随机投注系统
        self.system = OptimizedRandomBettingSystem(self.api_client, config)

        print(f"✅ LCG投注系统初始化完成!")
        print(f"   📊 预期避开率: 88.21%")
        print(f"   💰 基础投注: {config['base_bet_amount']}元")
        print(f"   🛡️ 最大投注: {config['max_bet_amount']}元")
        print(f"   ⚠️ 风险控制: 连败{config['max_consecutive_losses']}次止损")
        print(f"   🎯 投注模式: {'真实投注' if self.real_betting_mode else '模拟投注'}")

        return self.system

    def create_real_api_client(self) -> GameAPIClient:
        """创建真实API客户端"""

        # 真实API配置
        api_config = {
            'base_url': 'https://fks-api.lucklyworld.com',
            'headers': {
                'User-Agent': 'com.caike.union/5.2.2-official Dalvik/2.1.0 (Linux; U; Android 9; OPPO R9s Build/PQ3A.190605.04081832)',
                'packageId': 'com.caike.union',
                'version': '5.2.2',
                'channel': 'official',
                'androidId': 'e21953ffb86fa7a8',
                'userId': '8608623',
                'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiIiLCJhdWQiOiIiLCJqdGkiOiI4NjA4NjIzIiwiaWF0IjoxNzU0MTI0NzIyLCJuYmYiOjE3NTQxMjQ3MjIsImV4cCI6MTc1NjcxNjcyMiwidHlwZSI6InYxMWFwcCIsImF1dGhlbnRpY2F0aW9uU3RhdGUiOnRydWV9.pcOb7Fg07QnR30eXwORU6L7nDQL9rDgxSYOenF3ZU_Q',
                'IMEI': '',
                'ts': str(int(time.time() * 1000)),
                'sign': 'd0940ee13b368d3d8b8b60c7e56614b4ef02732d3214658394bb0ea13c31e277',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Host': 'fks-api.lucklyworld.com',
                'Connection': 'Keep-Alive',
                'Accept-Encoding': 'gzip'
            }
        }

        return GameAPIClient(api_config['base_url'], api_config['headers'])

    def init_real_betting_components(self):
        """初始化真实投注组件"""

        if not REAL_BETTING_AVAILABLE:
            return

        try:
            print("🔧 初始化真实投注组件...")

            # 预测规则适配器
            self.prediction_adapter = PredictionRuleAdapter()
            print("   ✅ 预测规则适配器已加载")

            # 盈利策略引擎
            self.profitable_strategy = ProfitableBettingStrategy()
            print("   ✅ 盈利策略引擎已加载")

            # 智能投注处理器
            self.smart_betting_handler = SmartBettingHandler(self.api_client)
            print("   ✅ 智能投注处理器已加载")

            print("🎯 真实投注组件初始化完成")

        except Exception as e:
            print(f"⚠️ 真实投注组件初始化失败: {e}")
            self.real_betting_mode = False

    def get_default_config(self) -> dict:
        """获取默认配置"""
        return {
            'base_bet_amount': 2,             # 基础投注金额 (整数，便于计算)
            'max_bet_amount': 20,             # 最大投注金额 (整数)
            'min_bet_amount': 1,              # 最小投注金额 (API限制，整数)
            'max_consecutive_losses': 5,       # 最大连续失败次数
            'max_daily_loss': 50,             # 日最大损失 (整数)
            'stop_loss_percentage': 0.3,       # 止损百分比
            'initial_balance': 200,           # 初始余额 (整数)
            'auto_report_interval': 10,       # 自动报告间隔 (每N次投注)
            'risk_monitoring': True,          # 启用风险监控
            'real_time_logging': True         # 启用实时日志
        }
    
    def create_mock_api_client(self):
        """创建模拟API客户端 (测试用)"""
        class MockAPIClient:
            def place_bet(self, room, amount):
                class MockResult:
                    def __init__(self):
                        self.success = True
                        self.total_amount = amount
                        self.data = {'room': room, 'amount': amount}
                        # 模拟偶尔的失败
                        if random.random() < 0.02:  # 2%失败率
                            self.success = False
                            self.message = "网络超时"
                return MockResult()
        
        return MockAPIClient()
    
    def run_interactive_mode(self):
        """运行交互模式"""
        
        if not self.system:
            print("❌ 系统未初始化，请先调用 initialize_system()")
            return
        
        print(f"\n🎮 进入交互模式")
        print(f"输入命令:")
        print(f"  'bet' - 执行一次投注")
        print(f"  'auto N' - 自动投注N期")
        print(f"  'report' - 生成报告")
        print(f"  'stats' - 显示统计")
        print(f"  'summary' - 显示摘要")
        print(f"  'quit' - 退出")
        print("-" * 40)
        
        current_issue = 124000
        
        while True:
            try:
                command = input(f"\n[期号{current_issue}] 请输入命令: ").strip().lower()
                
                if command == 'quit':
                    print("👋 正在退出...")
                    break
                
                elif command == 'bet':
                    self.execute_single_bet(current_issue)
                    current_issue += 1
                
                elif command.startswith('auto '):
                    try:
                        count = int(command.split()[1])
                        self.execute_auto_betting(current_issue, count)
                        current_issue += count
                    except (IndexError, ValueError):
                        print("❌ 格式错误，请使用: auto N (N为数字)")
                
                elif command == 'report':
                    self.system.generate_session_report()
                
                elif command == 'stats':
                    self.display_detailed_stats()
                
                elif command == 'summary':
                    print(f"\n{self.system.get_real_time_summary()}")
                
                else:
                    print("❌ 未知命令，请重新输入")
            
            except KeyboardInterrupt:
                print(f"\n\n⚠️ 检测到中断信号")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}")
        
        # 生成最终报告
        print(f"\n📝 正在生成最终报告...")
        self.system.generate_session_report()
        print(f"🎉 感谢使用LCG随机投注系统!")
    
    def execute_single_bet(self, issue: int):
        """执行单次投注"""
        print(f"\n🎯 执行第{issue}期投注...")

        if self.real_betting_mode:
            # 真实投注模式
            self.execute_real_betting(issue)
        else:
            # 模拟投注模式
            bet_info = self.system.execute_optimized_bet(issue)

            if bet_info:
                # 模拟开奖 (实际使用时从API获取)
                winning_room = random.randint(1, 8)
                print(f"🎰 模拟开奖: 房间{winning_room}")

                self.system.process_result(issue, winning_room)
                self.system.auto_generate_report_on_milestone()
            else:
                print("❌ 投注失败或被风控阻止")

    def execute_real_betting(self, issue: int):
        """执行真实投注"""

        if not self.real_betting_mode or not REAL_BETTING_AVAILABLE:
            print("❌ 真实投注模式未启用")
            return

        try:
            print(f"🎯 真实投注模式 - 第{issue}期")

            # 获取游戏状态
            game_state = self.api_client.get_game_state()
            if not game_state:
                print("❌ 无法获取游戏状态")
                return

            print(f"📊 游戏状态: 期号{game_state.issue}, 状态{game_state.state}, 倒计时{game_state.countdown}秒")

            # 检查是否为投注时机
            if game_state.state != 1 or game_state.countdown < 15:
                print(f"⏰ 非投注时机，跳过")
                return

            # 使用LCG算法选择房间
            lcg_room = self.system.select_optimal_random_room()

            # 记录房间选择
            log_room_selection(issue, list(range(1, 9)), lcg_room, "LCG算法", {"algorithm": "LCG"})

            # 计算投注金额 (现在返回元组: 金额, 计算详情)
            bet_amount, amount_calculation = self.system.calculate_enhanced_dynamic_amount()

            print(f"🎲 LCG选择房间: {lcg_room}")
            print(f"💰 投注金额: {bet_amount:.2f}元")

            # 执行智能投注
            if self.smart_betting_handler:
                bet_result = self.smart_betting_handler.execute_smart_bet(lcg_room, bet_amount)

                if bet_result.success:
                    print(f"✅ 投注成功: 房间{lcg_room}, 金额{bet_result.total_amount:.2f}元")

                    # 添加投注记录到系统历史 (重要：这样process_result才能找到记录)
                    bet_info = {
                        'issue': issue,
                        'room': lcg_room,
                        'amount': bet_result.total_amount,
                        'timestamp': time.time(),
                        'strategy': 'LCG算法'
                    }
                    self.system.bet_history.append(bet_info)
                    self.system.total_bets += 1

                    # 记录投注 (包含动态金额计算详情)
                    log_betting(issue, lcg_room, bet_result.total_amount, True, amount_calculation)

                    # 等待开奖结果
                    self.wait_for_result(issue, lcg_room, bet_result.total_amount)
                else:
                    print(f"❌ 投注失败: {bet_result.message}")
                    log_betting(issue, lcg_room, bet_amount, False, amount_calculation)
            else:
                print("❌ 智能投注处理器未初始化")

        except Exception as e:
            print(f"❌ 真实投注执行失败: {e}")

    def wait_for_result(self, issue: int, bet_room: int, bet_amount: float):
        """等待开奖结果"""

        print(f"⏳ 等待第{issue}期开奖结果...")

        max_wait_time = 120  # 最大等待2分钟
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            try:
                game_state = self.api_client.get_game_state()

                if game_state and game_state.state == 2 and game_state.kill_number > 0:
                    # 已开奖
                    winning_room = game_state.kill_number
                    print(f"🎰 第{issue}期开奖: 房间{winning_room}")

                    # 使用API返回的实际收益数据
                    actual_profit = game_state.my_win_stroke
                    print(f"💰 API返回收益: {actual_profit:.5f}元")

                    # 先更新系统状态 (传递API真实收益)
                    if game_state.my_is_win == 1:
                        # 获胜时使用API真实收益
                        self.system.process_result(issue, winning_room, actual_profit)
                    else:
                        # 失败时不需要传递收益 (系统会计算损失)
                        self.system.process_result(issue, winning_room)

                    # 然后记录到实时日志 (使用系统计算的实际盈亏)
                    if game_state.my_is_win == 1:
                        # 获胜 - 使用API返回的实际收益
                        print(f"🎉 投注获胜! 实际收益: {actual_profit:.5f}元")
                        log_result(issue, winning_room, "获胜", actual_profit, game_state.room_stats)
                    else:
                        # 失败 - 使用系统计算的损失
                        # 从最近的结果记录中获取系统计算的盈亏
                        system_loss = -bet_amount  # 系统内部已经处理了盈亏计算
                        print(f"😞 投注失败! 损失: {system_loss:.2f}元")
                        log_result(issue, winning_room, "失败", system_loss, game_state.room_stats)
                    return

                time.sleep(2)  # 每2秒检查一次

            except Exception as e:
                print(f"⚠️ 检查开奖结果失败: {e}")
                time.sleep(5)

        print(f"⏰ 等待开奖超时")
    
    def execute_auto_betting(self, start_issue: int, count: int):
        """执行自动投注"""
        print(f"\n🤖 开始自动投注 {count}期 (从第{start_issue}期开始)")
        
        successful_bets = 0
        
        for i in range(count):
            issue = start_issue + i
            print(f"\n--- 第{issue}期 ({i+1}/{count}) ---")
            
            bet_info = self.system.execute_optimized_bet(issue)
            
            if bet_info:
                successful_bets += 1
                
                # 模拟开奖
                winning_room = random.randint(1, 8)
                print(f"🎰 开奖: 房间{winning_room}")
                
                self.system.process_result(issue, winning_room)
                self.system.auto_generate_report_on_milestone()
                
                # 短暂延迟模拟真实投注间隔
                time.sleep(0.1)
            else:
                print("❌ 投注被阻止")
            
            # 每5期显示一次摘要
            if (i + 1) % 5 == 0:
                print(f"\n{self.system.get_real_time_summary()}")
        
        print(f"\n🏁 自动投注完成!")
        print(f"   成功投注: {successful_bets}/{count}期")
        print(f"   成功率: {successful_bets/count*100:.1f}%")
    
    def display_detailed_stats(self):
        """显示详细统计"""
        stats = self.system.get_enhanced_statistics()
        
        print(f"\n📊 详细统计信息:")
        print(f"=" * 50)
        
        for key, value in stats.items():
            if isinstance(value, float):
                if 'rate' in key or 'roi' in key:
                    print(f"   {key}: {value:.2f}%")
                else:
                    print(f"   {key}: {value:.4f}")
            else:
                print(f"   {key}: {value}")
        
        print(f"=" * 50)
    
    def run_demo_mode(self, periods: int = 20):
        """运行演示模式"""

        if not self.system:
            self.initialize_system()

        print(f"\n🎬 演示模式: 自动运行{periods}期投注")
        print(f"展示LCG算法和完整报告功能")
        print("-" * 60)

        self.execute_auto_betting(124000, periods)

        # 生成最终报告
        print(f"\n📝 生成最终演示报告...")
        report_file = self.system.generate_session_report()

        print(f"\n🎉 演示完成!")
        print(f"📄 详细报告: {report_file}")
        print(f"💡 请查看生成的Markdown文件了解详细分析")

    def run_long_term_mode(self):
        """运行长期模式 - 持续执行投注"""

        if not self.system:
            self.initialize_system()

        print(f"\n🔄 长期运行模式")
        print(f"系统将持续执行投注，直到触发停止条件")
        print(f"停止条件:")
        print(f"  - 连续失败达到限制")
        print(f"  - 余额低于安全线")
        print(f"  - 日损失达到上限")
        print(f"  - 手动中断 (Ctrl+C)")
        print("-" * 60)

        current_issue = 124000
        total_periods = 0
        last_report_time = time.time()
        report_interval = 300  # 每5分钟生成一次报告

        try:
            while True:
                # 检查是否应该继续投注
                if not self.system.should_place_bet():
                    print(f"\n🛑 触发停止条件，长期运行结束")
                    break

                print(f"\n--- 第{current_issue}期 (总计第{total_periods + 1}期) ---")

                # 执行投注
                bet_info = self.system.execute_optimized_bet(current_issue)

                if bet_info:
                    # 模拟开奖 (实际使用时从API获取)
                    winning_room = random.randint(1, 8)
                    print(f"🎰 开奖: 房间{winning_room}")

                    self.system.process_result(current_issue, winning_room)
                    self.system.auto_generate_report_on_milestone()

                    total_periods += 1
                    current_issue += 1

                    # 定时生成报告
                    current_time = time.time()
                    if current_time - last_report_time >= report_interval:
                        print(f"\n⏰ 定时报告 (运行{total_periods}期)")
                        self.system.generate_session_report()
                        last_report_time = current_time

                    # 每期显示简要摘要
                    if total_periods % 5 == 0:
                        print(f"\n{self.system.get_real_time_summary()}")

                    # 投注间隔 (模拟真实投注节奏)
                    time.sleep(1)  # 1秒间隔

                else:
                    print(f"❌ 投注被阻止，等待下一期...")
                    current_issue += 1
                    time.sleep(2)  # 失败时等待更长时间

        except KeyboardInterrupt:
            print(f"\n\n⚠️ 用户手动中断长期运行")

        except Exception as e:
            print(f"\n❌ 长期运行发生错误: {e}")

        finally:
            # 生成最终报告
            print(f"\n📝 生成长期运行最终报告...")
            report_file = self.system.generate_session_report()

            print(f"\n🏁 长期运行结束!")
            print(f"   总投注期数: {total_periods}期")
            print(f"   运行时长: {time.time() - self.system.reporter.session_start_time.timestamp():.0f}秒")
            print(f"📄 详细报告: {report_file}")

    def run_continuous_monitoring_mode(self):
        """运行持续监控模式 - 等待开奖信号"""

        if not self.system:
            self.initialize_system()

        print(f"\n📡 持续监控模式")
        print(f"系统将等待开奖信号，自动执行投注")
        print(f"适合连接真实API的长期运行")
        print("-" * 60)

        current_issue = 124000

        try:
            while True:
                print(f"\n⏳ 等待第{current_issue}期开奖信号...")

                # 模拟等待开奖信号 (实际使用时监听API)
                time.sleep(5)  # 模拟等待时间

                # 检查是否应该投注
                if self.system.should_place_bet():
                    print(f"\n🎯 执行第{current_issue}期投注")

                    bet_info = self.system.execute_optimized_bet(current_issue)

                    if bet_info:
                        # 等待开奖结果 (实际使用时从API获取)
                        print(f"⏳ 等待第{current_issue}期开奖结果...")
                        time.sleep(3)  # 模拟等待开奖时间

                        winning_room = random.randint(1, 8)
                        print(f"🎰 第{current_issue}期开奖: 房间{winning_room}")

                        self.system.process_result(current_issue, winning_room)
                        self.system.auto_generate_report_on_milestone()

                    current_issue += 1
                else:
                    print(f"⚠️ 风控阻止投注，跳过第{current_issue}期")
                    current_issue += 1

                # 每10期显示摘要
                if current_issue % 10 == 0:
                    print(f"\n{self.system.get_real_time_summary()}")

        except KeyboardInterrupt:
            print(f"\n\n⚠️ 用户手动中断监控模式")

        finally:
            print(f"\n📝 生成监控模式最终报告...")
            self.system.generate_session_report()
            print(f"🏁 监控模式结束!")

    def run_real_betting_monitoring_mode(self):
        """运行真实投注监控模式"""

        if not self.real_betting_mode or not REAL_BETTING_AVAILABLE:
            print("❌ 真实投注模式未启用")
            return

        print(f"\n🎯 真实投注监控模式")
        print(f"系统将连接真实API，自动监控游戏状态并执行投注")
        print(f"⚠️ 注意: 这将使用真实资金进行投注!")
        print("-" * 60)

        # 创建游戏监控器
        monitor = GameMonitor(self.api_client)

        try:
            print(f"🔄 开始监控游戏状态...")

            # 启动监控
            monitor.start_monitoring(self.process_real_game_state)

        except KeyboardInterrupt:
            print(f"\n\n⚠️ 用户手动中断真实投注监控")

        except Exception as e:
            print(f"\n❌ 真实投注监控发生错误: {e}")
            import traceback
            traceback.print_exc()

        finally:
            print(f"\n📝 生成真实投注监控最终报告...")
            if self.system:
                self.system.generate_session_report()
            print(f"🏁 真实投注监控结束!")

    def process_real_game_state(self, state: GameState):
        """处理真实游戏状态"""

        print(f"\n📊 游戏状态更新: 期号{state.issue}, 状态{state.state}, 倒计时{state.countdown}秒")

        if state.state == 1:  # 等待开奖 - 投注时机
            self.handle_real_betting_opportunity(state)
        elif state.state == 2:  # 已开奖 - 分析结果
            self.handle_real_lottery_result(state)

    def handle_real_betting_opportunity(self, state: GameState):
        """处理真实投注时机"""

        print(f"🎯 投注时机: 期号{state.issue}, 倒计时{state.countdown}秒")

        # 投注时机判断
        if not (15 <= state.countdown <= 30):
            print(f"⏰ 投注时机不佳，倒计时{state.countdown}秒")
            return

        # 风险控制检查
        if not self.system.should_place_bet():
            print(f"⚠️ 风控阻止投注")
            return

        # 执行真实投注
        self.execute_real_betting(state.issue)

    def handle_real_lottery_result(self, state: GameState):
        """处理真实开奖结果"""

        if state.kill_number > 0:
            print(f"🎰 第{state.issue}期开奖: 房间{state.kill_number}")

            # 显示API返回的收益信息
            if state.my_win_stroke > 0:
                print(f"💰 当期收益: {state.my_win_stroke:.5f}元")

            # 检查是否有对应的投注记录需要更新
            self.update_betting_result_with_api_data(state)

            # 更新系统状态 (传递API真实收益)
            if state.my_is_win == 1 and state.my_win_stroke > 0:
                # 获胜时使用API真实收益
                self.system.process_result(state.issue, state.kill_number, state.my_win_stroke)
            else:
                # 失败时不需要传递收益
                self.system.process_result(state.issue, state.kill_number)
            self.system.auto_generate_report_on_milestone()

    def update_betting_result_with_api_data(self, state: GameState):
        """使用API数据更新投注结果"""

        try:
            # 导入记录函数
            from real_time_logger import log_result

            # 判断输赢并记录实际收益
            if state.my_is_win == 1 and state.my_win_stroke > 0:
                # 获胜 - 使用API返回的实际收益
                print(f"📝 更新投注记录: 期号{state.issue}, 获胜, 实际收益{state.my_win_stroke:.5f}元")
                log_result(state.issue, state.kill_number, "获胜", state.my_win_stroke, state.room_stats)
            elif state.my_is_win == 0:
                # 失败 - 需要从投注记录中获取投注金额
                bet_amount = self.get_bet_amount_for_issue(state.issue)
                if bet_amount > 0:
                    print(f"📝 更新投注记录: 期号{state.issue}, 失败, 损失{bet_amount:.2f}元")
                    log_result(state.issue, state.kill_number, "失败", -bet_amount, state.room_stats)
                else:
                    print(f"📝 更新投注记录: 期号{state.issue}, 失败, 无投注记录")
                    log_result(state.issue, state.kill_number, "失败", 0.0, state.room_stats)
            else:
                print(f"📊 期号{state.issue}: 无投注或未获胜")

            # 显示房间统计信息
            if state.room_stats:
                print(f"🏠 房间统计信息:")
                print(f"   {'房间':<4} {'投入金额':<8} {'分享金额':<8} {'投入道具':<6} {'金额+道具':<10} {'人数':<4}")
                print(f"   {'-'*4} {'-'*8} {'-'*8} {'-'*6} {'-'*10} {'-'*4}")

                total_medal = 0.0
                total_share = 0.0
                total_buy = 0
                total_stroke = 0.0
                total_users = 0

                for room_num in range(1, 9):
                    if room_num in state.room_stats:
                        stats = state.room_stats[room_num]
                        medal = stats.get('total_medal', 0)
                        share = stats.get('share_medal', 0)
                        buy_stroke = stats.get('total_buy_stroke', 0)
                        stroke = stats.get('total_stroke', 0)
                        users = stats.get('user_count', 0)

                        total_medal += medal
                        total_share += share
                        total_buy += buy_stroke
                        total_stroke += stroke
                        total_users += users

                        # 验证计算公式: 投入金额*10 + 道具投入 = 总投入
                        expected_stroke = medal * 10 + buy_stroke
                        stroke_match = "✓" if abs(stroke - expected_stroke) < 0.1 else "✗"

                        print(f"   房间{room_num:<2} {medal:<8.1f} {share:<8.1f} {buy_stroke:<6} {stroke:<8.1f}{stroke_match:<2} {users:<4}")
                    else:
                        print(f"   房间{room_num:<2} {'0.0':<8} {'0.0':<8} {'0':<6} {'0.0':<10} {'0':<4}")

                print(f"   {'-'*4} {'-'*8} {'-'*8} {'-'*6} {'-'*10} {'-'*4}")
                # 验证总计算公式
                expected_total_stroke = total_medal * 10 + total_buy
                total_match = "✓" if abs(total_stroke - expected_total_stroke) < 0.1 else "✗"
                print(f"   总计   {total_medal:<8.1f} {total_share:<8.1f} {total_buy:<6} {total_stroke:<8.1f}{total_match:<2} {total_users:<4}")

        except Exception as e:
            print(f"❌ 更新投注结果失败: {e}")

    def get_bet_amount_for_issue(self, issue: int) -> float:
        """获取指定期号的投注金额"""

        try:
            if hasattr(self.system, 'bet_history') and self.system.bet_history:
                # 从系统投注历史中查找
                for bet in reversed(self.system.bet_history):
                    if bet.get('issue') == issue:
                        return bet.get('amount', 0.0)

            # 如果系统历史中没有，尝试从记录器中获取
            from real_time_logger import get_logger
            logger = get_logger()

            for record in reversed(logger.session_data['betting_records']):
                if record.get('issue') == issue and 'bet_amount' in record:
                    return record['bet_amount']

            return 0.0

        except Exception as e:
            print(f"⚠️ 获取投注金额失败: {e}")
            return 0.0


def main():
    """主函数"""

    # 创建主程序实例
    main_system = LCGBettingSystemMain()

    # 选择投注模式
    print(f"\n🎯 选择投注模式:")
    print(f"1. 模拟投注 - 安全的演示模式")
    if REAL_BETTING_AVAILABLE:
        print(f"2. 真实投注 - 连接真实API进行投注 ⚠️")

    betting_mode_choice = input(f"\n请选择投注模式 (1{'-2' if REAL_BETTING_AVAILABLE else ''}): ").strip()

    real_betting = False
    if REAL_BETTING_AVAILABLE and betting_mode_choice == '2':
        print(f"\n⚠️ 警告: 您选择了真实投注模式!")
        print(f"   - 这将使用真实资金进行投注")
        print(f"   - 请确保您了解投注风险")
        print(f"   - 建议先使用小额资金测试")

        confirm = input(f"\n确认启用真实投注模式? (yes/no): ").lower().strip()
        if confirm in ['yes', 'y', '是']:
            real_betting = True
            print(f"🎯 已启用真实投注模式")
        else:
            print(f"🎮 使用模拟投注模式")

    # 初始化系统
    system = main_system.initialize_system(real_betting=real_betting)

    print(f"\n🎯 选择运行模式:")
    print(f"1. 交互模式 - 手动控制投注")
    print(f"2. 演示模式 - 自动运行20期展示功能")
    print(f"3. 快速测试 - 运行测试并生成报告")
    print(f"4. 长期运行模式 - 持续执行投注直到停止条件 🔄")
    print(f"5. 持续监控模式 - 等待开奖信号自动投注 📡")
    if real_betting:
        print(f"6. 真实投注监控模式 - 连接真实API自动投注 🎯")

    try:
        max_choice = 6 if real_betting else 5
        choice = input(f"\n请选择模式 (1-{max_choice}): ").strip()

        if choice == '1':
            main_system.run_interactive_mode()

        elif choice == '2':
            main_system.run_demo_mode(20)

        elif choice == '3':
            print(f"\n🧪 快速测试模式")
            from optimized_random_betting_system import test_optimized_system
            test_optimized_system()

        elif choice == '4':
            print(f"\n🔄 启动长期运行模式...")
            main_system.run_long_term_mode()

        elif choice == '5':
            print(f"\n📡 启动持续监控模式...")
            main_system.run_continuous_monitoring_mode()

        elif choice == '6' and real_betting:
            print(f"\n🎯 启动真实投注监控模式...")
            main_system.run_real_betting_monitoring_mode()

        else:
            print(f"❌ 无效选择，运行默认演示模式")
            main_system.run_demo_mode(15)

    except KeyboardInterrupt:
        print(f"\n\n👋 程序被用户中断")

    except Exception as e:
        print(f"\n❌ 程序发生错误: {e}")

    finally:
        print(f"\n🎲 LCG随机投注系统已退出")


if __name__ == "__main__":
    main()
